#!/bin/bash

# RealMap Server Setup Script
# For servers with BT Panel / aaPanel

echo "🚀 Setting up RealMap on your server..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Server paths (adjust if different)
WEBROOT="/www/wwwroot/realmap.idezinelab.com"
NGINX_VHOST="/www/server/panel/vhost/nginx/realmap.idezinelab.com.conf"

echo -e "${BLUE}Server webroot: $WEBROOT${NC}"

# Check if we're in the right directory
if [ ! -d "realmap-frontend" ] || [ ! -d "realmap-backend" ]; then
    echo -e "${RED}❌ Error: realmap-frontend and realmap-backend directories not found${NC}"
    echo "Please run this script from the directory containing both folders"
    exit 1
fi

# Step 1: Build React Frontend
echo -e "\n${YELLOW}📦 Building React Frontend...${NC}"
cd realmap-frontend

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "Installing frontend dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Failed to install frontend dependencies${NC}"
        exit 1
    fi
fi

echo "Building React app..."
npm run build

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Frontend build completed successfully${NC}"
else
    echo -e "${RED}❌ Frontend build failed${NC}"
    exit 1
fi

cd ..

# Step 2: Setup Laravel Backend
echo -e "\n${YELLOW}⚙️ Setting up Laravel Backend...${NC}"
cd realmap-backend

# Install PHP dependencies
if [ ! -d "vendor" ]; then
    echo "Installing backend dependencies..."
    composer install --optimize-autoloader --no-dev
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Failed to install backend dependencies${NC}"
        exit 1
    fi
fi

# Setup environment file
if [ ! -f ".env" ]; then
    echo "Creating .env file..."
    cp .env.example .env
    php artisan key:generate
    echo -e "${YELLOW}⚠️ Please configure your .env file with database and other settings${NC}"
fi

# Create storage link
php artisan storage:link

echo -e "${GREEN}✅ Backend setup completed${NC}"

cd ..

# Step 3: Set correct permissions
echo -e "\n${YELLOW}🔐 Setting file permissions...${NC}"

# Frontend build permissions
chmod -R 755 realmap-frontend/build
chown -R www:www realmap-frontend/build

# Backend permissions
chmod -R 755 realmap-backend/storage
chmod -R 755 realmap-backend/bootstrap/cache
chmod -R 755 realmap-backend/public
chown -R www:www realmap-backend/storage
chown -R www:www realmap-backend/bootstrap/cache

echo -e "${GREEN}✅ Permissions set correctly${NC}"

# Step 4: Backup current Nginx config
echo -e "\n${YELLOW}💾 Backing up current Nginx configuration...${NC}"

if [ -f "$NGINX_VHOST" ]; then
    cp "$NGINX_VHOST" "${NGINX_VHOST}.backup.$(date +%Y%m%d_%H%M%S)"
    echo -e "${GREEN}✅ Current config backed up${NC}"
fi

# Step 5: Update Nginx configuration
echo -e "\n${YELLOW}🌐 Updating Nginx configuration...${NC}"

if [ -f "nginx-realmap-updated.conf" ]; then
    cp nginx-realmap-updated.conf "$NGINX_VHOST"
    echo -e "${GREEN}✅ Nginx configuration updated${NC}"
    
    # Test Nginx configuration
    echo "Testing Nginx configuration..."
    nginx -t
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Nginx configuration is valid${NC}"
        
        # Reload Nginx
        echo "Reloading Nginx..."
        systemctl reload nginx
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Nginx reloaded successfully${NC}"
        else
            echo -e "${RED}❌ Failed to reload Nginx${NC}"
        fi
    else
        echo -e "${RED}❌ Nginx configuration test failed${NC}"
        echo "Restoring backup..."
        cp "${NGINX_VHOST}.backup."* "$NGINX_VHOST"
    fi
else
    echo -e "${RED}❌ nginx-realmap-updated.conf not found${NC}"
fi

# Final instructions
echo -e "\n${GREEN}🎉 Setup completed!${NC}"
echo -e "\n${YELLOW}Important next steps:${NC}"
echo "1. Configure your database settings in realmap-backend/.env"
echo "2. Run database migrations: cd realmap-backend && php artisan migrate"
echo "3. Test your website: https://realmap.idezinelab.com"
echo "4. Backend admin panel: https://realmap.idezinelab.com/backend/admin"
echo ""
echo -e "${BLUE}URLs:${NC}"
echo "Frontend: https://realmap.idezinelab.com"
echo "Backend API: https://realmap.idezinelab.com/backend/api/"
echo "Admin Panel: https://realmap.idezinelab.com/backend/admin"
echo ""
echo -e "${YELLOW}If you encounter any issues:${NC}"
echo "- Check Nginx error logs: tail -f /www/wwwlogs/realmap.idezinelab.com.error.log"
echo "- Check Laravel logs: tail -f realmap-backend/storage/logs/laravel.log"
echo "- Restore Nginx backup if needed: cp ${NGINX_VHOST}.backup.* $NGINX_VHOST"
