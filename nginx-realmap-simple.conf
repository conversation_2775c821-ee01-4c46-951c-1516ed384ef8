server {
    listen 80;
    server_name realmap.idezinelab.com;
    
    # Root directory for frontend (React build files)
    root /path/to/your/project/realmap-frontend/build;
    index index.html;
    
    # Handle backend requests
    location /backend/ {
        alias /path/to/your/project/realmap-backend/public/;
        try_files $uri $uri/ @backend;
        
        # PHP configuration
        location ~ \.php$ {
            fastcgi_pass unix:/var/run/php/php8.2-fpm.sock; # Adjust PHP version as needed
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $request_filename;
            include fastcgi_params;
            fastcgi_param PATH_INFO $fastcgi_path_info;
        }
    }
    
    # Handle backend fallback (Laravel routing)
    location @backend {
        rewrite ^/backend/(.*)$ /backend/index.php?/$1 last;
    }
    
    # Handle static assets for backend
    location /backend/storage/ {
        alias /path/to/your/project/realmap-backend/storage/app/public/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Handle React Router (SPA routing)
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # Optimize file serving
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # Security: deny access to sensitive files
    location ~ /\. {
        deny all;
    }
    
    # Client max body size (for file uploads)
    client_max_body_size 100M;
}
