#!/bin/bash

# Fix Filament Assets Path Issues

echo "🔧 Fixing Filament Assets Path Issues..."

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

BACKEND_PATH="/www/wwwroot/realmap.idezinelab.com/realmap-backend"
NGINX_CONF="/www/server/panel/vhost/nginx/realmap.idezinelab.com.conf"

echo -e "${BLUE}Backend Path: $BACKEND_PATH${NC}"
echo -e "${BLUE}Nginx Config: $NGINX_CONF${NC}"

# Step 1: Backup current Nginx config
echo -e "\n${YELLOW}📋 Backing up current Nginx configuration...${NC}"
if [ -f "$NGINX_CONF" ]; then
    cp "$NGINX_CONF" "${NGINX_CONF}.backup.$(date +%Y%m%d_%H%M%S)"
    echo -e "${GREEN}✅ Backup created${NC}"
fi

# Step 2: Apply new Nginx configuration
echo -e "\n${YELLOW}🌐 Applying Filament assets-optimized Nginx configuration...${NC}"
if [ -f "nginx-filament-assets-fix.conf" ]; then
    cp nginx-filament-assets-fix.conf "$NGINX_CONF"
    echo -e "${GREEN}✅ New configuration applied${NC}"
else
    echo -e "${RED}❌ nginx-filament-assets-fix.conf not found${NC}"
    exit 1
fi

# Step 3: Test Nginx configuration
echo -e "\n${YELLOW}🧪 Testing Nginx configuration...${NC}"
nginx -t
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Nginx configuration is valid${NC}"
else
    echo -e "${RED}❌ Nginx configuration test failed${NC}"
    echo "Restoring backup..."
    cp "${NGINX_CONF}.backup."* "$NGINX_CONF"
    exit 1
fi

# Step 4: Setup Laravel backend
echo -e "\n${YELLOW}⚙️ Setting up Laravel backend...${NC}"
cd "$BACKEND_PATH"

# Clear all caches
echo "Clearing caches..."
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear

# Publish Filament assets
echo "Publishing Filament assets..."
php artisan filament:assets

# Check if assets were published correctly
echo -e "\n${YELLOW}🔍 Checking published Filament assets...${NC}"

ASSETS_TO_CHECK=(
    "public/css/filament/forms"
    "public/css/filament/filament"
    "public/css/filament/support"
    "public/js/filament/filament"
    "public/js/filament/support"
    "public/js/filament/notifications"
    "public/js/filament/tables"
    "public/livewire"
)

for asset in "${ASSETS_TO_CHECK[@]}"; do
    if [ -d "$asset" ] || [ -f "$asset" ]; then
        echo -e "${GREEN}✅ Found: $asset${NC}"
    else
        echo -e "${RED}❌ Missing: $asset${NC}"
    fi
done

# Step 5: Configure Filament asset URL
echo -e "\n${YELLOW}🔧 Configuring Filament asset URL...${NC}"

# Check if ASSET_URL is set in .env
if ! grep -q "ASSET_URL" .env; then
    echo "ASSET_URL=https://realmap.idezinelab.com" >> .env
    echo -e "${GREEN}✅ ASSET_URL added to .env${NC}"
else
    echo -e "${YELLOW}⚠️ ASSET_URL already exists in .env${NC}"
fi

# Cache new configuration
php artisan config:cache

# Step 6: Set proper permissions
echo -e "\n${YELLOW}🔐 Setting proper permissions...${NC}"
chmod -R 755 public/css/filament
chmod -R 755 public/js/filament
chmod -R 755 public/livewire
chown -R www:www public/css/filament
chown -R www:www public/js/filament
chown -R www:www public/livewire

# Step 7: Reload Nginx
echo -e "\n${YELLOW}🔄 Reloading Nginx...${NC}"
systemctl reload nginx
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Nginx reloaded successfully${NC}"
else
    echo -e "${RED}❌ Failed to reload Nginx${NC}"
fi

# Step 8: Test asset URLs
echo -e "\n${YELLOW}🧪 Testing Filament asset URLs...${NC}"

URLS_TO_TEST=(
    "https://realmap.idezinelab.com/css/filament/forms/forms.css"
    "https://realmap.idezinelab.com/css/filament/filament/app.css"
    "https://realmap.idezinelab.com/css/filament/support/support.css"
    "https://realmap.idezinelab.com/js/filament/filament/app.js"
    "https://realmap.idezinelab.com/js/filament/support/support.js"
    "https://realmap.idezinelab.com/js/filament/notifications/notifications.js"
    "https://realmap.idezinelab.com/livewire/livewire.js"
)

for url in "${URLS_TO_TEST[@]}"; do
    echo -n "Testing $url ... "
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$url")
    if [ "$HTTP_CODE" = "200" ]; then
        echo -e "${GREEN}✅ OK (200)${NC}"
    else
        echo -e "${RED}❌ FAILED ($HTTP_CODE)${NC}"
    fi
done

# Final summary
echo -e "\n${GREEN}🎉 Filament Assets Fix Complete!${NC}"
echo -e "\n${YELLOW}What was done:${NC}"
echo "1. ✅ Nginx configuration updated with direct asset paths"
echo "2. ✅ Laravel caches cleared"
echo "3. ✅ Filament assets published"
echo "4. ✅ Asset permissions set"
echo "5. ✅ ASSET_URL configured"
echo "6. ✅ Asset URLs tested"
echo ""
echo -e "${YELLOW}Key changes in Nginx:${NC}"
echo "- Added direct routes for /css/filament and /js/filament"
echo "- These routes bypass Laravel and serve files directly"
echo "- Improved caching for better performance"
echo ""
echo -e "${YELLOW}If you still have issues:${NC}"
echo "1. Check if assets exist: ls -la $BACKEND_PATH/public/css/filament/"
echo "2. Check Laravel logs: tail -f $BACKEND_PATH/storage/logs/laravel.log"
echo "3. Check Nginx error logs: tail -f /www/wwwlogs/realmap.idezinelab.com.error.log"
echo "4. Try republishing assets: php artisan filament:assets --force"
