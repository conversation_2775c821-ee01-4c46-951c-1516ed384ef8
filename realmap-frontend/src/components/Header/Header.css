.header {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1002;
  max-width: 600px;
  width: calc(100% - 40px);
  transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
}

.header-main {
  background: transparent;
  border-radius: 12px;
  overflow: hidden;
}

.header-tabs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-tab {
  flex: 1;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.95);
  border: none;
  color: #374151;
  font-size: 16px;
  font-weight: 600;
  font-family: 'Cairo', '<PERSON>ra Sans', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  /* backdrop-filter: blur(10px); */
}

.header-tab:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.header-tab.active {
  background: #3b82f6;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.search-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.search-tab-icon {
  font-size: 14px;
}

/* منطقة المحتوى المنسدل */
.header-dropdown {
  background: transparent;
  border-radius: 0 0 12px 12px;
  margin-top: 8px;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* أنماط البحث المنسدل */
.search-dropdown {
  padding: 0;
}

.search-form {
  display: flex;
  align-items: center;
  gap: 12px;
  background: transparent;
  padding: 16px;
  border-radius: 12px;
}

.search-dropdown-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.95);
  color: #374151;
  font-size: 16px;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  font-weight: 400;
  transition: all 0.3s ease;
  /* backdrop-filter: blur(10px); */
}

.search-dropdown-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-dropdown-input::placeholder {
  color: #9ca3af;
}

.search-dropdown-btn {
  padding: 12px 24px;
  background: rgba(59, 130, 246, 0.95);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  /* backdrop-filter: blur(10px); */
}

.search-dropdown-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* أنماط فلاتر الحالة المنسدلة */
.status-dropdown {
  padding: 16px;
  background: transparent;
  border-radius: 12px;
}

.status-title {
  color: white;
  font-size: 14px;
  font-weight: 500;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  margin-bottom: 12px;
  text-align: center;
  background: rgba(0, 0, 0, 0.5);
  padding: 8px 16px;
  border-radius: 8px;
  /* backdrop-filter: blur(10px); */
}

.status-filters-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  margin-bottom: 12px;
}

.status-filter-btn {
  padding: 12px 16px;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.status-filter-btn.all {
  background: #1f2937;
  color: white;
  border-color: #1f2937;
}

.status-filter-btn.all.active {
  background: #1f2937;
  border-color: white;
  border-width: 4px;
}

.status-filter-btn.sold {
  background: #fef3c7;
  color: #d97706;
  border-color: #fbbf24;
}

.status-filter-btn.sold.active {
  background: #fef3c7;
  color: #d97706;
  border-color: white;
  border-width: 4px;
}

.status-filter-btn.reserved {
  background: #fed7aa;
  color: #ea580c;
  border-color: #fb923c;
}

.status-filter-btn.reserved.active {
  background: #fed7aa;
  color: #ea580c;
  border-color: white;
  border-width: 4px;
}

.status-filter-btn.available {
  background: #d1fae5;
  color: #059669;
  border-color: #34d399;
}

.status-filter-btn.available.active {
  background: #d1fae5;
  color: #059669;
  border-color: white;
  border-width: 4px;
}

.status-count {
  text-align: center;
  color: white;
  font-size: 14px;
  font-weight: 500;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  background: rgba(0, 0, 0, 0.5);
  padding: 8px 16px;
  border-radius: 8px;
  /* backdrop-filter: blur(10px); */
}

/* أنماط فلتر السعر */
.price-dropdown {
  padding: 16px;
  background: transparent;
  border-radius: 12px;
}

.price-title {
  color: white;
  font-size: 14px;
  font-weight: 500;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  margin-bottom: 12px;
  text-align: center;
  background: rgba(0, 0, 0, 0.5);
  padding: 8px 16px;
  border-radius: 8px;
  /* backdrop-filter: blur(10px); */
}

.price-inputs-container {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.price-input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.price-input-group label {
  color: white;
  font-size: 12px;
  font-weight: 500;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  background: rgba(0, 0, 0, 0.5);
  padding: 4px 8px;
  border-radius: 4px;
  text-align: center;
  /* backdrop-filter: blur(10px); */
}

.price-input {
  padding: 10px 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  color: #374151;
  font-size: 14px;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  font-weight: 400;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.price-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.apply-filter-btn {
  width: 100%;
  padding: 12px 24px;
  background: rgba(59, 130, 246, 0.95);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  /* backdrop-filter: blur(10px); */
}

.apply-filter-btn:hover {
  background: rgba(37, 99, 235, 0.95);
  transform: translateY(-1px);
}

/* أنماط فلتر الاستخدام */
.usage-dropdown {
  padding: 16px;
  background: transparent;
  border-radius: 12px;
}

.usage-title {
  color: white;
  font-size: 14px;
  font-weight: 500;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  margin-bottom: 12px;
  text-align: center;
  background: rgba(0, 0, 0, 0.5);
  padding: 8px 16px;
  border-radius: 8px;
  /* backdrop-filter: blur(10px); */
}

.usage-filters-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
  margin-bottom: 12px;
}

.usage-filter-btn {
  padding: 12px 16px;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.usage-filter-btn.all {
  background: #1f2937;
  color: white;
  border-color: #1f2937;
}

.usage-filter-btn.all.active {
  background: #1f2937;
  border-color: white;
  border-width: 4px;
}

.usage-filter-btn.residential {
  background: #dbeafe;
  color: #1d4ed8;
  border-color: #60a5fa;
}

.usage-filter-btn.residential.active {
  background: #dbeafe;
  color: #1d4ed8;
  border-color: white;
  border-width: 4px;
}

.usage-filter-btn.commercial {
  background: #fef3c7;
  color: #d97706;
  border-color: #fbbf24;
}

.usage-filter-btn.commercial.active {
  background: #fef3c7;
  color: #d97706;
  border-color: white;
  border-width: 4px;
}

.usage-filter-btn.mixed {
  background: #e0e7ff;
  color: #7c3aed;
  border-color: #a78bfa;
}

.usage-filter-btn.mixed.active {
  background: #e0e7ff;
  color: #7c3aed;
  border-color: white;
  border-width: 4px;
}

.usage-filter-btn.industrial {
  background: #f3f4f6;
  color: #374151;
  border-color: #9ca3af;
}

.usage-filter-btn.industrial.active {
  background: #f3f4f6;
  color: #374151;
  border-color: white;
  border-width: 4px;
}

/* الأنماط المتجاوبة للهواتف المحمولة */
@media (max-width: 768px) {
  .header {
    top: 15px;
    max-width: calc(100% - 20px);
  }

  .header-tab {
    padding: 10px 12px;
    font-size: 14px;
  }

  .search-dropdown-input {
    font-size: 16px;
    /* منع التكبير في iOS */
  }

  .status-filters-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }

  .status-filter-btn {
    padding: 10px 12px;
    font-size: 13px;
  }

  .usage-filters-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 6px;
  }

  .usage-filter-btn {
    padding: 10px 12px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .header {
    top: 10px;
    max-width: calc(100% - 10px);
  }

  .header-tab {
    padding: 8px 10px;
    font-size: 12px;
  }

  .search-tab-icon {
    font-size: 12px;
  }

  .search-dropdown {
    padding: 12px;
  }

  .search-dropdown-input {
    padding: 10px 12px;
    font-size: 16px;
  }

  .search-dropdown-btn {
    padding: 10px 16px;
    font-size: 14px;
  }

  .status-dropdown {
    padding: 12px;
  }

  .status-filters-grid {
    grid-template-columns: 1fr 1fr;
    gap: 4px;
  }

  .status-filter-btn {
    padding: 8px 10px;
    font-size: 12px;
  }

  .status-title,
  .status-count {
    font-size: 12px;
  }

  .price-inputs-container {
    gap: 8px;
  }

  .price-input {
    padding: 8px 10px;
    font-size: 13px;
  }

  .price-input-group label {
    font-size: 11px;
  }

  .apply-filter-btn {
    padding: 10px 16px;
    font-size: 14px;
  }

  .usage-filters-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 4px;
  }

  .usage-filter-btn {
    padding: 8px 10px;
    font-size: 12px;
  }

  .price-title,
  .usage-title {
    font-size: 12px;
  }
}

/* دعم RTL */
.rtl .header-tabs {
  direction: rtl;
}

.rtl .search-form {
  direction: rtl;
}

.rtl .status-filters-grid {
  direction: rtl;
}

.rtl .usage-filters-grid {
  direction: rtl;
}