import React from "react";
import NorthArrow from "./NorthArrow";
import { API_CONFIG } from "../../config/constants";
import "./RightControls.css";

const RightControls = ({ mapRef, language = "ar" }) => {
  const handleLoginClick = () => {
    // Redirect to admin panel
    const adminUrl = `${API_CONFIG.BASE_URL}/admin`;
    window.open(adminUrl, '_blank');
  };

  return (
    <div className="right-controls">
      {/* North Arrow */}
      <NorthArrow mapRef={mapRef} />
      
      {/* Login Button */}
      <button
        className="control-btn login-btn"
        onClick={handleLoginClick}
        title={language === 'ar' ? 'تسجيل الدخول' : 'Login'}
      >
        <span className="control-icon">
          <img src="/map_controls/Login v2.png" alt="Login" className="control-icon-img" />
        </span>
      </button>
    </div>
  );
};

export default RightControls;
