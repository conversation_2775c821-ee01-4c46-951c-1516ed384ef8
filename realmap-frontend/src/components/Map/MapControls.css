.map-controls {
  position: absolute;
  top: 27%;
  left: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .map-controls {
    top: 27%;
    left: 15px;
    bottom: auto;
    transform: translateY(-50%);
    flex-direction: column;
    gap: 8px;
    /* background: rgba(31, 41, 55, 0.95); */
    padding: 8px;
    border-radius: 12px;
    /* backdrop-filter: blur(15px); */
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}


@media (max-width: 480px) {
  .map-controls {
    left: 10px;
    gap: 6px;
    padding: 6px;
    border-radius: 10px;
  }
}

.control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  /* background: rgba(31, 41, 55, 0.95); */
  border: 1px solid rgba(31, 41, 55, 0.3);
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  font-family: '<PERSON>ra Sans', 'Cairo', sans-serif;
  color: white;
  transition: all 0.3s ease;
  /* backdrop-filter: blur(15px); */
  box-shadow: 0 4px 12px rgba(31, 41, 55, 0.2);
  min-width: 48px;
  min-height: 48px;
  position: relative;
  overflow: hidden;
}

/* Mobile responsive button sizes */
@media (max-width: 768px) {
  .control-btn {
    min-width: 40px;
    min-height: 40px;
    padding: 8px;
    border-radius: 8px;
    font-size: 12px;
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .control-label {
    display: none;
    /* Hide labels on mobile */
  }

  .control-icon-img {
    width: 20px;
    height: 20px;
    object-fit: contain;
    /* filter: brightness(0) saturate(100%) invert(27%) sepia(8%) saturate(1115%) hue-rotate(169deg) brightness(94%) contrast(86%); Dark gray color */
  }

  /* Map Style Toggle responsive */
  .map-style-toggle {
    padding: 6px;
    min-width: 44px;
    border-radius: 6px;
  }

  .map-style-toggle .control-icon {
    font-size: 16px;
  }

  /* Reset View responsive */
  .reset-view {
    padding: 6px;
    min-width: 44px;
    border-radius: 6px;
  }

  .reset-view .control-icon-img {
    width: 32px;
    height: 38px;
  }

  .language-toggle {
    min-height: 65px;
    min-width: 32px;
  }

  .language-text {
    font-size: 12px;
  }

  .language-text.next {
    font-size: 10px;
  }

  .zoom-controls {
    display: flex;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .control-btn {
    min-width: 36px;
    min-height: 36px;
    padding: 6px;
    border-radius: 6px;
    font-size: 11px;
  }

  .zoom-controls {
    gap: 2px;
  }

  .control-icon-img {
    width: 18px;
    height: 18px;
  }

  /* Map Style Toggle responsive */
  .map-style-toggle {
    padding: 4px;
    min-width: 40px;
    border-radius: 4px;
  }

  .map-style-toggle .control-icon {
    font-size: 14px;
  }

  /* Reset View responsive */
  .reset-view {
    padding: 4px;
    min-width: 40px;
    border-radius: 4px;
  }

  .reset-view .control-icon-img {
    width: 28px;
    height: 34px;
  }

  .language-toggle {
    min-height: 55px;
    min-width: 28px;
  }

  .language-text {
    font-size: 11px;
  }

  .language-text.next {
    font-size: 9px;
  }
}

.control-btn:hover {
  background: rgba(31, 41, 55, 1);
  box-shadow: 0 6px 20px rgba(31, 41, 55, 0.3);
  transform: translateY(-2px);
}

.control-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(31, 41, 55, 0.2);
}

.control-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.control-btn:hover::before {
  left: 100%;
}

.control-icon {
  font-size: 24px;
  min-width: 48px;
  text-align: center;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-icon-img {
  width: 24px;
  height: 24px;
  object-fit: contain;
  /* filter: brightness(0) saturate(100%) invert(27%) sepia(8%) saturate(1115%) hue-rotate(169deg) brightness(94%) contrast(86%); */
  /* Dark gray color */
}

.control-label {
  font-size: 13px;
  font-family: 'Fira Sans', 'Cairo', sans-serif;
  font-weight: 500;
  white-space: nowrap;
  display: none;
}

/* Specific button styles */
.sidebar-toggle {
  /* background: rgba(31, 41, 55, 0.95); */
  color: white;
  border-color: rgba(31, 41, 55, 0.3);
}

.sidebar-toggle:hover {
  background: rgba(31, 41, 55, 1);
  box-shadow: 0 6px 20px rgba(31, 41, 55, 0.4);
}

/* Map Style Toggle - Match NorthArrow design */
.map-style-toggle {
  position: relative;
  overflow: hidden;
  min-width: 60px;
  min-height: 60px;
  padding: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: transparent;
  color: white;
}

.map-style-toggle:hover {
  background-color: rgba(31, 41, 55, 1);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(31, 41, 55, 0.3);
}

.map-style-toggle:active {
  transform: translateY(0);
}

.map-style-toggle .control-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  font-size: 24px;
}

/* Light effect on hover */
.map-style-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.map-style-toggle:hover::before {
  left: 100%;
}

.language-toggle {
  background: rgba(255, 255, 255, 0.95);
  color: #374151;
  border-color: rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  padding: 6px 4px;
  min-width: 36px;
  min-height: 80px;
}

.language-toggle:hover {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.segment-vertical {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.language-segment {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
  width: 100%;
}

.language-text {
  font-size: 14px;
  font-weight: 600;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  line-height: 1;
  padding: 4px 0;
  transition: all 0.3s ease;
}

.language-text.current {
  color: #374151;
  opacity: 1;
}

.language-text.next {
  color: #9ca3af;
  opacity: 0.7;
  font-size: 12px;
}

.language-divider {
  width: 20px;
  height: 1px;
  background: #d1d5db;
  margin: 2px 0;
}

/* Reset View Button - Match NorthArrow design */
.reset-view {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 50px;
  background: transparent;
}

.reset-view:hover {
  background-color: rgba(31, 41, 55, 1);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(31, 41, 55, 0.3);
}

.reset-view:active {
  transform: translateY(0);
}

.reset-view .control-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.reset-view .control-icon-img {
  width: 40px;
  height: 48px;
  object-fit: contain;
  filter: none;
  /* Show original colors */
}



/* Zoom controls */
.zoom-controls {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.zoom-btn {
  min-width: 48px;
  min-height: 48px;
  padding: 12px;
  justify-content: center;
  /* background: rgba(31, 41, 55, 0.95); */
  border: 1px solid rgba(31, 41, 55, 0.3);
  color: white;
}

.zoom-btn:hover {
  background: rgba(31, 41, 55, 1);
  box-shadow: 0 6px 20px rgba(31, 41, 55, 0.3);
}

.zoom-btn .control-icon {
  font-size: 20px;
  font-weight: bold;
  min-width: auto;
}

.zoom-in {
  border-radius: 12px 12px 4px 4px;
}

.zoom-out {
  border-radius: 4px 4px 12px 12px;
  border-top: 1px solid rgba(30, 58, 138, 0.3);
}

/* Very small screens */
@media (max-width: 375px) {
  .map-style-toggle {
    padding: 3px;
    min-width: 36px;
  }

  .map-style-toggle .control-icon {
    font-size: 12px;
  }

  .reset-view {
    padding: 3px;
    min-width: 36px;
  }

  .reset-view .control-icon-img {
    width: 24px;
    height: 30px;
  }
}

/* RTL Support */
.rtl .map-controls {
  left: auto;
  right: 20px;
}

@media (max-width: 768px) {
  .rtl .map-controls {
    left: auto;
    right: 15px;
    transform: translateY(-50%);
  }
}

@media (max-width: 480px) {
  .rtl .map-controls {
    right: 10px;
  }
}

/* Animation for sidebar toggle */
.sidebar-toggle .control-icon {
  transition: transform 0.2s ease;
}

.sidebar-toggle:hover .control-icon {
  transform: scale(1.1);
}

/* Pulse animation for important buttons */
@keyframes pulse {
  0% {
    box-shadow: 0 4px 12px rgba(31, 41, 55, 0.2);
  }

  50% {
    box-shadow: 0 6px 20px rgba(31, 41, 55, 0.4);
  }

  100% {
    box-shadow: 0 4px 12px rgba(31, 41, 55, 0.2);
  }
}

.sidebar-toggle.pulse {
  animation: pulse 2s infinite;
}

/* Glow effect for active buttons */
@keyframes glow {
  0% {
    box-shadow: 0 4px 12px rgba(31, 41, 55, 0.2);
  }

  50% {
    box-shadow: 0 6px 20px rgba(31, 41, 55, 0.4), 0 0 20px rgba(31, 41, 55, 0.3);
  }

  100% {
    box-shadow: 0 4px 12px rgba(31, 41, 55, 0.2);
  }
}

.control-btn.active {
  animation: glow 1.5s ease-in-out infinite;
}

/* Satellite icon specific styles */
.satellite-icon {
  width: 48px;
  height: 48px;
  color: currentColor;
  transition: all 0.3s ease;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.control-btn:hover .satellite-icon {
  transform: scale(1.1) rotate(5deg);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.map-style-toggle:active .satellite-icon {
  transform: scale(0.95) rotate(-2deg);
}