import React from "react";
import { FaMap, FaPlus, FaMinus, FaHome, FaGlobe } from "react-icons/fa";
import "./MapControls.css";
import { getTranslation } from "../../utils/translations";

// شعار القمر الصناعي المخصص - تصميم محسن
const SatelliteIcon = () => (
  <svg 
    width="48" 
    height="48" 
    viewBox="0 0 24 24" 
    fill="none"
    className="satellite-icon"
  >
    {/* جسم القمر الصناعي الرئيسي */}
    <rect 
      x="9" 
      y="9" 
      width="6" 
      height="6" 
      rx="1"
      fill="currentColor"
      stroke="currentColor"
      strokeWidth="0.5"
    />
    
    {/* الألواح الشمسية */}
    <rect x="5" y="7" width="4" height="10" rx="0.5" fill="none" stroke="currentColor" strokeWidth="1.5"/>
    <rect x="15" y="7" width="4" height="10" rx="0.5" fill="none" stroke="currentColor" strokeWidth="1.5"/>
    
    {/* خطوط الألواح الشمسية */}
    <line x1="5.5" y1="8" x2="8.5" y2="8" stroke="currentColor" strokeWidth="0.5"/>
    <line x1="5.5" y1="9.5" x2="8.5" y2="9.5" stroke="currentColor" strokeWidth="0.5"/>
    <line x1="5.5" y1="11" x2="8.5" y2="11" stroke="currentColor" strokeWidth="0.5"/>
    <line x1="5.5" y1="12.5" x2="8.5" y2="12.5" stroke="currentColor" strokeWidth="0.5"/>
    <line x1="5.5" y1="14" x2="8.5" y2="14" stroke="currentColor" strokeWidth="0.5"/>
    <line x1="5.5" y1="15.5" x2="8.5" y2="15.5" stroke="currentColor" strokeWidth="0.5"/>
    
    <line x1="15.5" y1="8" x2="18.5" y2="8" stroke="currentColor" strokeWidth="0.5"/>
    <line x1="15.5" y1="9.5" x2="18.5" y2="9.5" stroke="currentColor" strokeWidth="0.5"/>
    <line x1="15.5" y1="11" x2="18.5" y2="11" stroke="currentColor" strokeWidth="0.5"/>
    <line x1="15.5" y1="12.5" x2="18.5" y2="12.5" stroke="currentColor" strokeWidth="0.5"/>
    <line x1="15.5" y1="14" x2="18.5" y2="14" stroke="currentColor" strokeWidth="0.5"/>
    <line x1="15.5" y1="15.5" x2="18.5" y2="15.5" stroke="currentColor" strokeWidth="0.5"/>
    
    {/* الهوائي */}
    <circle cx="12" cy="10" r="1" fill="none" stroke="currentColor" strokeWidth="1"/>
    <line x1="12" y1="9" x2="12" y2="6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    <circle cx="12" cy="6" r="0.5" fill="currentColor"/>
    
    {/* إشارات الاتصال */}
    <path d="M2 2L4 4" stroke="currentColor" strokeWidth="1" strokeLinecap="round" opacity="0.7"/>
    <path d="M20 2L22 4" stroke="currentColor" strokeWidth="1" strokeLinecap="round" opacity="0.7"/>
    <path d="M2 22L4 20" stroke="currentColor" strokeWidth="1" strokeLinecap="round" opacity="0.7"/>
    <path d="M20 22L22 20" stroke="currentColor" strokeWidth="1" strokeLinecap="round" opacity="0.7"/>
    
    {/* دوائر الإشارة */}
    <circle cx="12" cy="12" r="4" fill="none" stroke="currentColor" strokeWidth="0.5" opacity="0.3"/>
    <circle cx="12" cy="12" r="6" fill="none" stroke="currentColor" strokeWidth="0.5" opacity="0.2"/>
  </svg>
);

const MapControls = ({
  mapStyle,
  onMapStyleChange,
  onLanguageToggle,
  language,
  mapRef,
}) => {
  const mapStyles = [
    {
      id: "satellite-v9",
      name: getTranslation("satellite", language),
      icon: <SatelliteIcon />,
    },
    {
      id: "streets-v11",
      name: getTranslation("streets", language),
      icon: <FaMap />,
    },
  ];

  const currentStyleIndex = mapStyles.findIndex(
    (style) => style.id === mapStyle
  );

  const handleMapStyleToggle = () => {
    const nextIndex = (currentStyleIndex + 1) % mapStyles.length;
    const newStyle = mapStyles[nextIndex].id;
    console.log("MapControls: Toggling from", mapStyle, "to", newStyle);
    onMapStyleChange(newStyle);
  };

  return (
    <div className="map-controls">
      {/* Map Style Toggle Button */}
     

      {/* Language Toggle Button */}
      <button
        className="control-btn language-toggle segment-vertical"
        onClick={onLanguageToggle}
        title={getTranslation("changeLanguage", language)}
      >
        <div className="language-segment">
          <span className="language-text current">{language === 'ar' ? 'ع' : 'E'}</span>
          <div className="language-divider"></div>
          <span className="language-text next">{language === 'ar' ? 'En' : 'ع'}</span>
        </div>
      </button>

       <button
        className="control-btn map-style-toggle"
        onClick={() => {
          console.log("Map style button clicked!");
          handleMapStyleToggle();
        }}
        title={getTranslation("changeMapStyle", language)}
      >
        <span className="control-icon">
          {mapStyles[currentStyleIndex]?.icon || <FaMap />}
        </span>
      </button>
      {/* Zoom Controls */}
      {/* <div className="zoom-controls">
        <button
          className="control-btn zoom-btn zoom-in"
          onClick={() => mapRef?.current?.zoomIn()}
          title={getTranslation("zoomIn", language)}
        >
          <span className="control-icon">
            <FaPlus />
          </span>
        </button>
        <button
          className="control-btn zoom-btn zoom-out"
          onClick={() => mapRef?.current?.zoomOut()}
          title={getTranslation("zoomOut", language)}
        >
          <span className="control-icon">
            <FaMinus />
          </span>
        </button>
      </div> */}

      {/* Reset View Button */}
      <button
        className="control-btn reset-view"
        onClick={() => {
          if (mapRef?.current) {
            mapRef.current.flyTo({
              center: [45.0792, 23.8859], // Saudi Arabia center
              zoom: 6,
            });
          }
        }}
        title={getTranslation("resetView", language)}
      >
        <span className="control-icon">
          <img src="/map_controls/Home icon.png" alt="Home" className="control-icon-img" />
        </span>
      </button>
    </div>
  );
};

export default MapControls;
