import React, { useState, useEffect } from "react";

const LoadingOverlay = ({ isVisible, progress, message, language = "ar" }) => {
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  const [isTextVisible, setIsTextVisible] = useState(true);

  // النصوص العشوائية
  const loadingTexts = {
    ar: [
      "شاهد كل وحدة بمخططها الهندسي وتفاصيلها الدقيقة",
      "كل مشروع على الخريطة كما هو على الأرض… بدون تجميل",
      "فلترة ذكية حسب الموقع، الاستخدام، السعر والحالة",
      "من الخريطة للحجز المباشر… دون وسطاء أو تعقيد",
      "تابع تطور المشروع على الخريطة خطوة بخطوة، في البيع على الخارطة",
      "اعثر على أفضل الفرص حسب العائد الاستثماري المتوقع",
      "أعد بيع وحدتك بسهولة… من نفس الخريطة"
    ],
    en: [
      "Explore Every Unit in Detail",
      "See the Project as It Is",
      "Smart Filtering for Smarter Decisions",
      "Book Directly from the Map",
      "Track Project Progress Live",
      "Maximize Your ROI",
      "Resell with One Tap"
    ]
  };

  // تبديل النصوص كل ثانية
  useEffect(() => {
    if (!isVisible) return;

          const interval = setInterval(() => {
        setIsTextVisible(false);
        
        setTimeout(() => {
          setCurrentTextIndex(prev => 
            (prev + 1) % loadingTexts[language].length
          );
          setIsTextVisible(true);
        }, 300);
      }, 2000);

    return () => clearInterval(interval);
  }, [isVisible, language]);

  // إعادة تعيين الفهرس عند تغيير اللغة
  useEffect(() => {
    setCurrentTextIndex(0);
  }, [language]);

  if (!isVisible) return null;

  return (
    <div className="loading-overlay">
      {/* Background with Gradient */}
      <div className="loading-background" />
      
      {/* Content Container */}
      <div className="loading-content">
        {/* Modern Loading Animation */}
        <div className="loading-animation">
          {/* Animated Rings */}
          <div className="loading-ring ring-1"></div>
          <div className="loading-ring ring-2"></div>
          <div className="loading-ring ring-3"></div>
          
          {/* Central Logo */}
          <div className="loading-logo">
            <img
              src="/logo_only.png"
              alt="RealMap Logo"
              className="logo-image"
            />
          </div>
        </div>
        
        {/* Dynamic Text */}
        <div className="loading-text-container">
          <div 
            className={`loading-text ${isTextVisible ? 'visible' : 'hidden'}`}
            key={currentTextIndex}
          >
            {loadingTexts[language][currentTextIndex]}
          </div>
        </div>
        
        {/* Modern Progress Bar */}
        <div className="progress-container">
          <div className="progress-bar">
            <div 
              className="progress-fill"
              style={{ width: `${progress}%` }}
            />
            <div className="progress-glow" style={{ left: `${progress}%` }} />
          </div>
          <div className="progress-text">
            {Math.round(progress)}%
          </div>
        </div>
        
        {/* Floating Particles */}
        <div className="particles">
          {[...Array(20)].map((_, i) => (
            <div 
              key={i} 
              className="particle" 
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${3 + Math.random() * 2}s`
              }}
            />
          ))}
        </div>
      </div>
      
      {/* Modern CSS Styles */}
      <style>
        {`
          .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 10000;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          }
          
                     .loading-background {
             position: absolute;
             top: 0;
             left: 0;
             right: 0;
             bottom: 0;
             background: linear-gradient(135deg, 
               rgba(15, 23, 42, 0.4) 0%, 
               rgba(30, 41, 59, 0.5) 50%, 
               rgba(15, 23, 42, 0.4) 100%);
             backdrop-filter: blur(3px);
           }
          
          .loading-content {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            z-index: 1;
          }
          
          .loading-animation {
            position: relative;
            width: 120px;
            height: 120px;
            margin-bottom: 40px;
          }
          
          .loading-ring {
            position: absolute;
            border-radius: 50%;
            border: 2px solid transparent;
            animation: rotate 3s linear infinite;
          }
          
          .ring-1 {
            width: 120px;
            height: 120px;
            border-top: 2px solid #007cbf;
            border-right: 2px solid rgba(0, 124, 191, 0.3);
            animation-duration: 2s;
          }
          
          .ring-2 {
            width: 90px;
            height: 90px;
            top: 15px;
            left: 15px;
            border-top: 2px solid #00d4ff;
            border-left: 2px solid rgba(0, 212, 255, 0.3);
            animation-duration: 1.5s;
            animation-direction: reverse;
          }
          
          .ring-3 {
            width: 60px;
            height: 60px;
            top: 30px;
            left: 30px;
            border-top: 2px solid #0ea5e9;
            border-bottom: 2px solid rgba(14, 165, 233, 0.3);
            animation-duration: 1s;
          }
          
                     .loading-logo {
             position: absolute;
             top: 50%;
             left: 50%;
             transform: translate(-50%, -50%);
             width: 40px;
             height: 40px;
             background: rgba(255, 255, 255, 0.05);
             border-radius: 50%;
             display: flex;
             align-items: center;
             justify-content: center;
             backdrop-filter: blur(2px);
             box-shadow: 0 8px 32px rgba(0, 124, 191, 0.2);
           }
          
          .logo-image {
            width: 28px;
            height: 28px;
            object-fit: contain;
            filter: brightness(1.2);
          }
          
          .loading-text-container {
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
          }
          
          .loading-text {
            font-size: 20px;
            font-weight: 600;
            color: #ffffff;
            max-width: 500px;
            line-height: 1.4;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            transform: translateY(0);
          }
          
          .loading-text.visible {
            opacity: 1;
            transform: translateY(0);
          }
          
          .loading-text.hidden {
            opacity: 0;
            transform: translateY(10px);
          }
          
          .progress-container {
            width: 320px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
          }
          
          .progress-bar {
            position: relative;
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
          }
          
          .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007cbf, #00d4ff, #0ea5e9);
            border-radius: 10px;
            transition: width 0.5s ease;
            position: relative;
          }
          
          .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 20px;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4));
            animation: shimmer 2s infinite;
          }
          
          .progress-glow {
            position: absolute;
            top: -2px;
            width: 12px;
            height: 10px;
            background: radial-gradient(circle, rgba(0, 212, 255, 0.8), transparent);
            border-radius: 50%;
            transform: translateX(-50%);
            transition: left 0.5s ease;
          }
          
          .progress-text {
            font-size: 16px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            letter-spacing: 1px;
          }
          
          .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
          }
          
                     .particle {
             position: absolute;
             width: 4px;
             height: 4px;
             background: radial-gradient(circle, rgba(0, 212, 255, 0.4), transparent);
             border-radius: 50%;
             animation: float linear infinite;
           }
          
          @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
          
          @keyframes shimmer {
            0% { transform: translateX(-20px); }
            100% { transform: translateX(100px); }
          }
          
          @keyframes float {
            0% {
              transform: translateY(100vh) scale(0);
              opacity: 0;
            }
            10% {
              opacity: 1;
            }
            90% {
              opacity: 1;
            }
            100% {
              transform: translateY(-10px) scale(1);
              opacity: 0;
            }
          }
          
          /* Mobile Responsive */
          @media (max-width: 768px) {
            .loading-animation {
              width: 100px;
              height: 100px;
            }
            
            .ring-1 { width: 100px; height: 100px; }
            .ring-2 { width: 75px; height: 75px; top: 12.5px; left: 12.5px; }
            .ring-3 { width: 50px; height: 50px; top: 25px; left: 25px; }
            
            .loading-text {
              font-size: 18px;
              max-width: 300px;
              padding: 0 20px;
            }
            
            .progress-container {
              width: 280px;
            }
          }
        `}
      </style>
    </div>
  );
};

export default LoadingOverlay;
