pipeline {
    agent any

    stages {
        stage('Deployment') {
            steps {
                script {
                     // Your shell script or Groovy code here
                    echo "Deployment started... "

                    // Change directory to /www/wwwroot/erp and run the deployment script
                    sh '''
                    git config --global --add safe.directory /www/wwwroot/realmap.idezinelab.com

                    cd /www/wwwroot/realmap.idezinelab.com
                    # Pull the latest version of the app
                    git pull origin main
                    # Put the application in maintenance mode
                    cd /www/wwwroot/realmap.idezinelab.com/realmap-backend
                    /www/server/php/82/bin/php artisan down || true

                    # Install composer dependencies
                    #/www/server/php/82/bin/php /usr/bin/composer install --no-dev --no-interaction --prefer-dist --optimize-autoloader --ignore-platform-req=ext-fileinfo
                    php -d disable_functions= /usr/local/bin/composer update --ignore-platform-req=ext-fileinfo

                    # Clear the old cache
                    /www/server/php/82/bin/php artisan clear-compiled

                    # Recreate cache
                    /www/server/php/82/bin/php artisan optimize:clear

                    # Run database migrations if needed
                     /www/server/php/82/bin/php artisan migrate 

                    # Exit maintenance mode
                    /www/server/php/82/bin/php artisan up
                    # Uncomment and run npm prod build if needed
                    # npm run production
                    
                    cd /www/wwwroot/realmap.idezinelab.com/realmap-frontend/
                    npm run build
                   
                    '''

                    echo "Deployment finished!"
                }
            }
        }
    }
}