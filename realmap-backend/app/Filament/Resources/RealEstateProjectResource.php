<?php

namespace App\Filament\Resources;

use App\Filament\Resources\RealEstateProjectResource\Pages;
use App\Filament\Resources\RealEstateProjectResource\RelationManagers;
use App\Models\RealEstateProject;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class RealEstateProjectResource extends Resource
{
    protected static ?string $model = RealEstateProject::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office-2';

    protected static ?string $navigationLabel = 'Real Estate Projects';

    protected static ?string $modelLabel = 'Project';

    protected static ?string $navigationGroup = 'Real Estate Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Project Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('location')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('developer_name')
                            ->maxLength(255),
                        Forms\Components\FileUpload::make('developer_logo')
                            ->label('Developer Logo')
                            ->image()
                            ->disk('public')
                            ->directory('developer-logos')
                            ->visibility('public')
                            ->acceptedFileTypes(['image/png', 'image/jpg', 'image/jpeg', 'image/svg+xml'])
                            ->maxSize(2048) // 2MB
                            ->imageResizeMode('contain')
                            ->imageResizeTargetWidth('200')
                            ->imageResizeTargetHeight('200')
                            ->helperText('Upload a logo for the developer (PNG, JPG, JPEG, SVG). Recommended size: 200x200px'),
                        Forms\Components\FileUpload::make('default_unit_image')
                            ->label('Default Unit Image / صورة الوحدة الافتراضية')
                            ->image()
                            ->disk('public')
                            ->directory('default-unit-images')
                            ->visibility('public')
                            ->acceptedFileTypes(['image/png', 'image/jpg', 'image/jpeg', 'image/webp'])
                            ->maxSize(5120) // 5MB
                            ->imageResizeMode('cover')
                            ->imageCropAspectRatio('16:9')
                            ->imageResizeTargetWidth('800')
                            ->imageResizeTargetHeight('450')
                            ->helperText('Upload a default image for units without specific images (recommended: 800x450px)')
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('city')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('geo_area')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('latitude')
                            ->label('Latitude')
                            ->numeric()
                            // ->step(0.00000001)
                            ->helperText('Enter the latitude coordinate for the project location'),
                        Forms\Components\TextInput::make('longitude')
                            ->label('Longitude')
                            ->numeric()
                            // ->step(0.00000001)
                            ->helperText('Enter the longitude coordinate for the project location'),
                        Forms\Components\Select::make('user_id')
                            ->relationship('user', 'name')
                            ->required(),
                        Forms\Components\Toggle::make('active')
                            ->label('Active / نشط')
                            ->helperText('Toggle to show/hide this project on the map')
                            ->default(true)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('GeoJSON Files')
                    ->description('Upload GeoJSON files to import blocks and parcels data (files will not be saved)')
                    ->schema([
                        Forms\Components\FileUpload::make('blocks_geojson_file')
                            ->label('Blocks GeoJSON File')
                            // ->acceptedFileTypes(['application/json', '.geojson', '.json'])
                            ->maxSize(10240) // 10MB
                            ->helperText('Upload a GeoJSON file containing block boundaries and properties')
                            ->columnSpanFull()
                            ->disk('local')
                            ->directory('temp-geojson')
                            ->visibility('private')
                            ->storeFileNamesIn('blocks_geojson_file_name'),

                        Forms\Components\FileUpload::make('parcels_geojson_file')
                            ->label('Parcels GeoJSON File')
                            // ->acceptedFileTypes(['application/json', '.geojson', '.json'])
                            ->maxSize(10240) // 10MB
                            ->helperText('Upload a GeoJSON file containing parcel boundaries and properties')
                            ->columnSpanFull()
                            ->disk('local')
                            ->directory('temp-geojson')
                            ->visibility('private')
                            ->storeFileNamesIn('parcels_geojson_file_name'),
                    ])
                    ->collapsible(),

                Forms\Components\Section::make('Dynamic Labels & Content')
                    ->description('Customize labels and content that appear in the project details panel')
                    ->schema([
                        Forms\Components\Tabs::make('Content Tabs')
                            ->tabs([
                                Forms\Components\Tabs\Tab::make('Descriptions')
                                    ->schema([
                                        Forms\Components\Textarea::make('description_ar')
                                            ->label('Description (Arabic)')
                                            ->placeholder('أراضي سكنية واستثمارية في قلب الخرج')
                                            ->columnSpanFull(),
                                        Forms\Components\Textarea::make('description_en')
                                            ->label('Description (English)')
                                            ->placeholder('Residential and investment land in the heart of Al-Kharj')
                                            ->columnSpanFull(),
                                    ]),

                                Forms\Components\Tabs\Tab::make('Units & Availability')
                                    ->schema([
                                        Forms\Components\TextInput::make('total_units')
                                            ->label('Total Units')
                                            ->numeric()
                                            ->placeholder('1828'),
                                        Forms\Components\TextInput::make('virtual_tour_url')
                                            ->label('360° Virtual Tour URL')
                                            ->url()
                                            ->placeholder('https://example.com/360-tour')
                                            ->helperText('Enter the URL for the 360° virtual tour (will open when 360° icon is clicked)')
                                            ->columnSpanFull(),
                                        Forms\Components\TextInput::make('share_url')
                                            ->label('Custom Share URL')
                                            ->url()
                                            ->placeholder('https://example.com/project-details')
                                            ->helperText('Enter a custom URL to share (optional - if empty, current page URL will be used)')
                                            ->columnSpanFull(),
                                        Forms\Components\TextInput::make('units_label_ar')
                                            ->label('Units Label (Arabic)')
                                            ->placeholder('وحدة')
                                            ->default('وحدة'),
                                        Forms\Components\TextInput::make('units_label_en')
                                            ->label('Units Label (English)')
                                            ->placeholder('units')
                                            ->default('units'),
                                        Forms\Components\TextInput::make('availability_label_ar')
                                            ->label('Availability Label (Arabic)')
                                            ->placeholder('المتاح')
                                            ->default('المتاح'),
                                        Forms\Components\TextInput::make('availability_label_en')
                                            ->label('Availability Label (English)')
                                            ->placeholder('Available')
                                            ->default('Available'),
                                    ])->columns(2),

                                Forms\Components\Tabs\Tab::make('Pricing')
                                    ->schema([
                                        Forms\Components\TextInput::make('min_price')
                                            ->label('Minimum Price')
                                            ->numeric()
                                            ->placeholder('200000'),
                                        Forms\Components\TextInput::make('max_price')
                                            ->label('Maximum Price')
                                            ->numeric()
                                            ->placeholder('500000'),
                                        Forms\Components\TextInput::make('price_prefix_ar')
                                            ->label('Price Prefix (Arabic)')
                                            ->placeholder('يبدأ من')
                                            ->default('يبدأ من'),
                                        Forms\Components\TextInput::make('price_prefix_en')
                                            ->label('Price Prefix (English)')
                                            ->placeholder('Starting from')
                                            ->default('Starting from'),
                                        Forms\Components\TextInput::make('price_label_ar')
                                            ->label('Price Label (Arabic)')
                                            ->placeholder('الأسعار')
                                            ->default('الأسعار'),
                                        Forms\Components\TextInput::make('price_label_en')
                                            ->label('Price Label (English)')
                                            ->placeholder('Prices')
                                            ->default('Prices'),
                                    ])->columns(2),

                                Forms\Components\Tabs\Tab::make('Facilities')
                                    ->schema([
                                        Forms\Components\Textarea::make('facilities_ar')
                                            ->label('Facilities (Arabic)')
                                            ->placeholder('مساجد - مدارس - مواقف - حدائق')
                                            ->columnSpanFull(),
                                        Forms\Components\Textarea::make('facilities_en')
                                            ->label('Facilities (English)')
                                            ->placeholder('Mosques - Schools - Parking - Gardens')
                                            ->columnSpanFull(),
                                        Forms\Components\TextInput::make('facilities_label_ar')
                                            ->label('Facilities Label (Arabic)')
                                            ->placeholder('المرافق')
                                            ->default('المرافق'),
                                        Forms\Components\TextInput::make('facilities_label_en')
                                            ->label('Facilities Label (English)')
                                            ->placeholder('Facilities')
                                            ->default('Facilities'),
                                    ])->columns(2),

                                Forms\Components\Tabs\Tab::make('Owner')
                                    ->schema([
                                        Forms\Components\TextInput::make('owner_ar')
                                            ->label('Owner (Arabic)')
                                            ->placeholder('منصة أصل العقارية')
                                            ->columnSpanFull(),
                                        Forms\Components\TextInput::make('owner_en')
                                            ->label('Owner (English)')
                                            ->placeholder('ASSL Real Estate Platform')
                                            ->columnSpanFull(),
                                        Forms\Components\TextInput::make('owner_label_ar')
                                            ->label('Owner Label (Arabic)')
                                            ->placeholder('المالك')
                                            ->default('المالك'),
                                        Forms\Components\TextInput::make('owner_label_en')
                                            ->label('Owner Label (English)')
                                            ->placeholder('Owner')
                                            ->default('Owner'),
                                    ])->columns(2),
                            ])
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('location')
                    ->searchable(),
                Tables\Columns\TextColumn::make('developer_name')
                    ->searchable(),
                Tables\Columns\ImageColumn::make('developer_logo')
                    ->label('Logo')
                    ->disk('public')
                    ->height(40)
                    ->width(40)
                    ->circular(),
                Tables\Columns\TextColumn::make('city')
                    ->searchable(),
                Tables\Columns\TextColumn::make('geo_area')
                    ->searchable(),
                Tables\Columns\IconColumn::make('active')
                    ->label('Active / نشط')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                Tables\Columns\TextColumn::make('latitude')
                    ->label('Lat')
                    // ->numeric(decimalPlaces: 6)
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('longitude')
                    ->label('Lng')
                    // ->numeric(decimalPlaces: 6)
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('blocks_count')
                    ->label('Blocks')
                    ->counts('blocks')
                    ->badge(),
                Tables\Columns\TextColumn::make('user.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('active')
                    ->label('Active Status / حالة النشاط')
                    ->placeholder('All Projects / جميع المشاريع')
                    ->trueLabel('Active Only / النشطة فقط')
                    ->falseLabel('Inactive Only / غير النشطة فقط'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\BlocksRelationManager::class,
            RelationManagers\MapLayersRelationManager::class,
            RelationManagers\LandmarksRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRealEstateProjects::route('/'),
            'create' => Pages\CreateRealEstateProject::route('/create'),
            'edit' => Pages\EditRealEstateProject::route('/{record}/edit'),
        ];
    }
}
