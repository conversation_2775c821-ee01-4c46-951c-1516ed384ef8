<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RealEstateProject extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'description_ar',
        'description_en',
        'location',
        'developer_name',
        'developer_logo',
        'default_unit_image',
        'city',
        'geo_area',
        'latitude',
        'longitude',
        'user_id',
        'active',
        'blocks_geojson_file',
        'parcels_geojson_file',
        'blocks_geojson_file_name',
        'parcels_geojson_file_name',
        // Dynamic labels
        'units_label_ar',
        'units_label_en',
        'availability_label_ar',
        'availability_label_en',
        'price_prefix_ar',
        'price_prefix_en',
        'price_label_ar',
        'price_label_en',
        'facilities_ar',
        'facilities_en',
        'facilities_label_ar',
        'facilities_label_en',
        'owner_ar',
        'owner_en',
        'owner_label_ar',
        'owner_label_en',
        'total_units',
        'min_price',
        'max_price',
        'virtual_tour_url',
        'share_url',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'total_units' => 'integer',
        'min_price' => 'decimal:2',
        'max_price' => 'decimal:2',
    ];

    /**
     * Get the user that owns the project.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the blocks for the project.
     */
    public function blocks(): HasMany
    {
        return $this->hasMany(Block::class, 'project_id');
    }

    /**
     * Get the map layers for the project.
     */
    public function mapLayers(): HasMany
    {
        return $this->hasMany(MapLayer::class, 'project_id');
    }

    /**
     * Get the landmarks for the project.
     */
    public function landmarks(): HasMany
    {
        return $this->hasMany(Landmark::class, 'project_id');
    }

    /**
     * Get the full URL for the developer logo.
     */
    public function getDeveloperLogoUrlAttribute(): ?string
    {
        if (!$this->developer_logo) {
            return null;
        }

        // Use the full URL with the correct base URL
        return url('storage/' . $this->developer_logo);
    }

    /**
     * Get the full URL for the default unit image.
     */
    public function getDefaultUnitImageUrlAttribute(): ?string
    {
        if (!$this->default_unit_image) {
            return null;
        }

        // Use the full URL with the correct base URL
        return url('storage/' . $this->default_unit_image);
    }
}
