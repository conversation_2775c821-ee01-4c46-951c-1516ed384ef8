#!/bin/bash

# RealMap Setup Script
# This script helps you set up the RealMap project on your server

echo "🚀 Setting up RealMap Project..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Get current directory
PROJECT_DIR=$(pwd)

echo -e "${YELLOW}Project directory: $PROJECT_DIR${NC}"

# Step 1: Build React Frontend
echo -e "\n${YELLOW}📦 Building React Frontend...${NC}"
cd realmap-frontend

if [ ! -d "node_modules" ]; then
    echo "Installing frontend dependencies..."
    npm install
fi

echo "Building React app..."
npm run build

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Frontend build completed successfully${NC}"
else
    echo -e "${RED}❌ Frontend build failed${NC}"
    exit 1
fi

cd ..

# Step 2: Setup Laravel Backend
echo -e "\n${YELLOW}⚙️ Setting up <PERSON><PERSON> Backend...${NC}"
cd realmap-backend

# Install PHP dependencies
if [ ! -d "vendor" ]; then
    echo "Installing backend dependencies..."
    composer install --optimize-autoloader --no-dev
fi

# Setup environment file
if [ ! -f ".env" ]; then
    echo "Creating .env file..."
    cp .env.example .env
    php artisan key:generate
    echo -e "${YELLOW}⚠️ Please configure your .env file with database and other settings${NC}"
fi

# Create storage link
php artisan storage:link

# Run migrations (optional - uncomment if needed)
# php artisan migrate --force

echo -e "${GREEN}✅ Backend setup completed${NC}"

cd ..

# Step 3: Set correct permissions
echo -e "\n${YELLOW}🔐 Setting file permissions...${NC}"

# Frontend build permissions
chmod -R 755 realmap-frontend/build

# Backend permissions
chmod -R 755 realmap-backend/storage
chmod -R 755 realmap-backend/bootstrap/cache
chmod -R 755 realmap-backend/public

echo -e "${GREEN}✅ Permissions set correctly${NC}"

# Step 4: Generate Nginx configuration
echo -e "\n${YELLOW}🌐 Generating Nginx configuration...${NC}"

# Replace placeholder paths in nginx config
sed "s|/path/to/your/project|$PROJECT_DIR|g" nginx-realmap-simple.conf > nginx-realmap-configured.conf

echo -e "${GREEN}✅ Nginx configuration generated: nginx-realmap-configured.conf${NC}"

# Final instructions
echo -e "\n${GREEN}🎉 Setup completed successfully!${NC}"
echo -e "\n${YELLOW}Next steps:${NC}"
echo "1. Copy nginx-realmap-configured.conf to your Nginx sites-available directory"
echo "2. Create a symbolic link to sites-enabled"
echo "3. Test and reload Nginx configuration"
echo ""
echo "Commands to run on your server:"
echo "sudo cp nginx-realmap-configured.conf /etc/nginx/sites-available/realmap"
echo "sudo ln -s /etc/nginx/sites-available/realmap /etc/nginx/sites-enabled/"
echo "sudo nginx -t"
echo "sudo systemctl reload nginx"
echo ""
echo -e "${YELLOW}Don't forget to:${NC}"
echo "- Configure your .env file in realmap-backend/"
echo "- Set up your database"
echo "- Configure SSL certificates if needed"
echo "- Adjust PHP-FPM socket path in Nginx config if different"
