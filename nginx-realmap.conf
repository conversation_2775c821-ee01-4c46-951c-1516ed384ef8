server {
    listen 80;
    server_name realmap.idezinelab.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name realmap.idezinelab.com;
    
    # SSL Configuration (adjust paths as needed)
    ssl_certificate /path/to/your/ssl/certificate.crt;
    ssl_certificate_key /path/to/your/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Root directory for frontend (React build files)
    root /path/to/your/project/realmap-frontend/build;
    index index.html;
    
    # Handle backend API requests
    location /backend/ {
        alias /path/to/your/project/realmap-backend/public/;
        try_files $uri $uri/ @backend;
        
        # PHP configuration
        location ~ \.php$ {
            fastcgi_pass unix:/var/run/php/php8.2-fpm.sock; # Adjust PHP version as needed
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $request_filename;
            include fastcgi_params;
            fastcgi_param PATH_INFO $fastcgi_path_info;
        }
    }
    
    # Handle backend fallback (Laravel routing)
    location @backend {
        rewrite ^/backend/(.*)$ /backend/index.php?/$1 last;
    }
    
    # Handle API requests (if you have /api routes)
    location /api/ {
        alias /path/to/your/project/realmap-backend/public/;
        try_files $uri $uri/ @api;
        
        location ~ \.php$ {
            fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $request_filename;
            include fastcgi_params;
            fastcgi_param PATH_INFO $fastcgi_path_info;
        }
    }
    
    location @api {
        rewrite ^/api/(.*)$ /api/index.php?/$1 last;
    }
    
    # Handle static assets for backend
    location /backend/storage/ {
        alias /path/to/your/project/realmap-backend/storage/app/public/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Handle React Router (SPA routing)
    location / {
        try_files $uri $uri/ /index.html;
        expires 1h;
        add_header Cache-Control "public";
    }
    
    # Handle static assets for frontend
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Optimize file serving
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # Security: deny access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ /(vendor|storage|bootstrap|config|database|resources|routes|tests)/ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    # Client max body size (for file uploads)
    client_max_body_size 100M;
    
    # Timeouts
    fastcgi_read_timeout 300;
    fastcgi_connect_timeout 300;
    fastcgi_send_timeout 300;
}
