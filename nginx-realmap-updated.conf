server
{
    listen 80;
    listen 443 ssl http2;
    server_name realmap.idezinelab.com;
    index index.php index.html index.htm default.php default.htm default.html;
    
    # Root directory points to React frontend build
    root /www/wwwroot/realmap.idezinelab.com/realmap-frontend/build;

    #CERT-APPLY-CHECK--START
    # Configuration related to file verification for SSL certificate application - Do not delete
    include /www/server/panel/vhost/nginx/well-known/realmap.idezinelab.com.conf;
    #CERT-APPLY-CHECK--END
    
    #SSL-START SSL related configuration, do NOT delete or modify the next line of commented-out 404 rules
    #error_page 404/404.html;
        ssl_certificate    /www/server/panel/vhost/cert/realmap.idezinelab.com/fullchain.pem;
        ssl_certificate_key    /www/server/panel/vhost/cert/realmap.idezinelab.com/privkey.pem;
        ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
        ssl_ciphers EECDH+CHACHA20:EECDH+CHACHA20-draft:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;
        ssl_prefer_server_ciphers on;
        ssl_session_tickets on;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        add_header Strict-Transport-Security "max-age=31536000";
        error_page 497  https://$host$request_uri;
    #SSL-END

    # Handle Laravel backend requests
    location /backend/ {
        alias /www/wwwroot/realmap.idezinelab.com/realmap-backend/public/;
        try_files $uri $uri/ @backend;
        
        # PHP configuration for backend
        location ~ \.php$ {
            fastcgi_pass unix:/tmp/php-cgi-82.sock;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $request_filename;
            include fastcgi_params;
            fastcgi_param PATH_INFO $fastcgi_path_info;
        }
    }
    
    # Handle backend fallback (Laravel routing)
    location @backend {
        rewrite ^/backend/(.*)$ /backend/index.php?/$1 last;
    }
    
    # Handle API requests (if you have separate /api routes)
    location /api/ {
        alias /www/wwwroot/realmap.idezinelab.com/realmap-backend/public/;
        try_files $uri $uri/ @api;
        
        location ~ \.php$ {
            fastcgi_pass unix:/tmp/php-cgi-82.sock;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $request_filename;
            include fastcgi_params;
            fastcgi_param PATH_INFO $fastcgi_path_info;
        }
    }
    
    location @api {
        rewrite ^/api/(.*)$ /api/index.php?/$1 last;
    }
    
    # Handle storage files for backend
    location /backend/storage/ {
        alias /www/wwwroot/realmap.idezinelab.com/realmap-backend/storage/app/public/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Handle React Router (SPA routing) - This should be last
    location / {
        try_files $uri $uri/ /index.html;
        expires 1h;
        add_header Cache-Control "public";
    }

    #ERROR-PAGE-START  Error page configuration, allowed to be commented, deleted or modified
    error_page 404 /404.html;
    error_page 502 /502.html;
    #ERROR-PAGE-END

    #REWRITE-START URL rewrite rule reference, any modification will invalidate the rewrite rules set by the panel
    # include /www/server/panel/vhost/rewrite/realmap.idezinelab.com.conf;
    #REWRITE-END

    # Forbidden files or directories
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md)
    {
        return 404;
    }
    
    # Security: deny access to Laravel sensitive directories
    location ~ /(vendor|storage|bootstrap|config|database|resources|routes|tests)/ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Directory verification related settings for one-click application for SSL certificate
    location ~ \.well-known{
        allow all;
    }

    #Prohibit putting sensitive files in certificate verification directory
    if ( $uri ~ "^/\.well-known/.*\.(php|jsp|py|js|css|lua|ts|go|zip|tar\.gz|rar|7z|sql|bak)$" ) {
        return 403;
    }

    # Static files caching
    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf|ico|svg)$
    {
        expires      30d;
        error_log /dev/null;
        access_log /dev/null;
    }

    location ~ .*\.(js|css|woff|woff2|ttf|eot)?$
    {
        expires      12h;
        error_log /dev/null;
        access_log /dev/null; 
    }
    
    # Increase client max body size for file uploads
    client_max_body_size 100M;
    
    # Timeouts for PHP
    fastcgi_read_timeout 300;
    fastcgi_connect_timeout 300;
    fastcgi_send_timeout 300;
    
    access_log  /www/wwwlogs/realmap.idezinelab.com.log;
    error_log  /www/wwwlogs/realmap.idezinelab.com.error.log;
}
