<?php

// Laravel Asset Configuration for Filament
// Add this to your Laravel configuration

// File: config/app.php - Add to the configuration array
return [
    // ... other config

    /*
    |--------------------------------------------------------------------------
    | Asset URL
    |--------------------------------------------------------------------------
    |
    | This URL is used by the console to properly generate URLs when using
    | the Artisan command line tool. You should set this to the root of
    | your application so that it is used when running Artisan tasks.
    |
    */

    'asset_url' => env('ASSET_URL', 'https://realmap.idezinelab.com'),

    // ... rest of config
];

// File: config/filament.php - Add or modify this configuration
// If this file doesn't exist, create it with:
// php artisan vendor:publish --tag=filament-config

/*
return [
    'default_filesystem_disk' => env('FILAMENT_FILESYSTEM_DISK', 'public'),
    
    'assets_path' => null,
    
    'cache_path' => base_path('bootstrap/cache/filament'),
    
    'livewire_loading_delay' => 'default',
    
    'broadcasting' => [
        'echo' => [
            'broadcaster' => 'pusher',
            'key' => env('VITE_PUSHER_APP_KEY'),
            'cluster' => env('VITE_PUSHER_APP_CLUSTER'),
            'wsHost' => env('VITE_PUSHER_HOST'),
            'wsPort' => env('VITE_PUSHER_PORT', 443),
            'wssPort' => env('VITE_PUSHER_PORT', 443),
            'authEndpoint' => '/broadcasting/auth',
            'disableStats' => true,
            'encrypted' => true,
            'forceTLS' => true,
        ],
    ],
];
*/

// Environment variables to add to .env file:
/*
ASSET_URL=https://realmap.idezinelab.com
APP_URL=https://realmap.idezinelab.com
FILAMENT_FILESYSTEM_DISK=public
*/
